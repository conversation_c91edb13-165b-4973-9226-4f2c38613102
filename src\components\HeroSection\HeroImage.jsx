import { forwardRef } from 'react'

const HeroImage = forwardRef((props, ref) => {
  return (
    <div className="flex-1 flex items-center justify-center perspective-1000 lg:block hidden">
      <div
        ref={ref}
        className="w-[650px] h-[650px] bg-gradient-to-br from-primary/20 to-secondary/30 rounded-full opacity-0 animate-fade-in-up animation-delay-600 transition-transform duration-100 lg:w-[650px] lg:h-[650px] md:w-[500px] md:h-[500px] transform-none flex items-center justify-center"
      >
        <div className="w-[400px] h-[400px] bg-gradient-to-br from-primary/40 to-secondary/50 rounded-full flex items-center justify-center">
          <div className="w-[200px] h-[200px] bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
            <i className="fas fa-video text-white text-6xl"></i>
          </div>
        </div>
      </div>
    </div>
  )
})

HeroImage.displayName = 'HeroImage'

export default HeroImage
