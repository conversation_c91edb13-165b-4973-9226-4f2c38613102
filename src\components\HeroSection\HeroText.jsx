import { useState } from 'react'
import SearchForm from '../SearchForm/SearchForm'
import TrustedBy from '../TrustedBy/TrustedBy'
import GeminiButton from '../GeminiButton/GeminiButton'
import { useGeminiAPI } from '../../hooks/useGeminiAPI'

const HeroText = () => {
  const [headline, setHeadline] = useState('Professional Video Editing Made Simple')
  const [description, setDescription] = useState('Transform your raw footage into stunning cinematic masterpieces with our powerful, intuitive video editing tools designed for creators of all levels.')

  const { callGeminiAPI, isLoading } = useGeminiAPI()

  const generateHeadline = async () => {
    const prompt = `Generate one alternative, catchy headline for a video editing software or platform. The current headline is: "${headline}". Make it concise, powerful, and appealing to content creators and video editors.`
    const newHeadline = await callGeminiAPI(prompt)
    if (newHeadline) {
      setHeadline(newHeadline)
    }
  }

  const generateDescription = async () => {
    const prompt = `Based on the headline "${headline}", write a compelling one-sentence description for a video editing platform. The current description is: "${description}". Make it engaging, clear, and highlight the benefits for video creators.`
    const newDescription = await callGeminiAPI(prompt)
    if (newDescription) {
      setDescription(newDescription)
    }
  }

  return (
    <div className="flex-1 max-w-[600px] lg:max-w-[600px] lg:flex lg:flex-col lg:items-start w-full flex flex-col items-center">
      <p className="text-lg font-medium text-secondary mb-4 opacity-0 animate-fade-in-up animation-delay-200">
        Welcome To VideoStudio Pro
      </p>

      <div className="flex items-start gap-4 mb-6 opacity-0 animate-fade-in-up animation-delay-400 lg:justify-start justify-center">
        <h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight flex-grow">
          {headline}
        </h1>
        <GeminiButton
          onClick={generateHeadline}
          disabled={isLoading}
          ariaLabel="Generate new headline with AI"
        />
      </div>

      <div className="flex items-start gap-4 mb-8 opacity-0 animate-fade-in-up animation-delay-600 lg:justify-start justify-center">
        <p className="text-base text-text-muted max-w-[450px] flex-grow">
          {description}
        </p>
        <GeminiButton
          onClick={generateDescription}
          disabled={isLoading}
          ariaLabel="Generate new description with AI"
        />
      </div>

      <div className="opacity-0 animate-fade-in-up animation-delay-800 w-full lg:w-auto">
        <SearchForm />
      </div>

      <div className="opacity-0 animate-fade-in-up animation-delay-1000">
        <TrustedBy />
      </div>
    </div>
  )
}

export default HeroText
