import { useState } from 'react'

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const navLinks = [
    { href: '#', text: 'Home', active: true },
    { href: '#', text: 'About Us' },
    { href: '#', text: 'Services' },
    { href: '#', text: 'Our Story' },
    { href: '#', text: 'Contact Us' }
  ]

  return (
    <nav className="flex justify-between items-center w-full z-[100]">
      <div className="animate-fade-in">
        <a href="#hero" aria-label="Go to homepage">
          <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 0L27.0711 2.92893L37.0711 12.9289L40 20L37.0711 27.0711L27.0711 37.0711L20 40L12.9289 37.0711L2.92893 27.0711L0 20L2.92893 12.9289L12.9289 2.92893L20 0Z" fill="url(#paint0_linear_1_2)"/>
            <path d="M20 5L24.3301 7.5L32.5 17.5L35 20L32.5 22.5L24.3301 32.5L20 35L15.6699 32.5L7.5 22.5L5 20L7.5 17.5L15.6699 7.5L20 5Z" fill="url(#paint1_linear_1_2)"/>
            <defs>
              <linearGradient id="paint0_linear_1_2" x1="20" y1="0" x2="20" y2="40" gradientUnits="userSpaceOnUse">
                <stop stopColor="#A594FD"/>
                <stop offset="1" stopColor="#7646FF"/>
              </linearGradient>
              <linearGradient id="paint1_linear_1_2" x1="20" y1="5" x2="20" y2="35" gradientUnits="userSpaceOnUse">
                <stop stopColor="white" stopOpacity="0.2"/>
                <stop offset="1" stopColor="white" stopOpacity="0"/>
              </linearGradient>
            </defs>
          </svg>
        </a>
      </div>

      <div className={`flex items-center md:flex md:items-center md:static md:w-auto md:h-auto md:bg-transparent md:backdrop-blur-none md:flex-row md:justify-start md:transform-none md:transition-none
        ${isMenuOpen ? 'translate-x-0' : 'translate-x-full'}
        fixed top-0 right-0 w-[70%] h-screen bg-background/95 backdrop-blur-lg flex-col justify-center items-center transform transition-transform duration-400 ease-[cubic-bezier(0.23,1,0.32,1)]`}>
        <ul className="flex list-none gap-10 md:gap-10 md:flex-row flex-col text-center animate-fade-in animation-delay-200">
          {navLinks.map((link, index) => (
            <li key={index}>
              <a
                href={link.href}
                className={`no-underline font-medium text-base transition-colors duration-300 relative pb-1 md:text-base text-xl
                  ${link.active ? 'text-white after:w-full' : 'text-text-muted hover:text-white'}
                  after:content-[''] after:absolute after:bottom-0 after:left-0 after:h-0.5 after:bg-secondary after:transition-all after:duration-300 after:ease-out
                  ${link.active ? 'after:w-full' : 'after:w-0 hover:after:w-full'}`}
              >
                {link.text}
              </a>
            </li>
          ))}
        </ul>

      </div>

      <button
        className="md:hidden bg-transparent border-none text-white text-2xl cursor-pointer z-[101]"
        onClick={toggleMenu}
        aria-label="Toggle navigation menu"
      >
        <i className={`fa-solid ${isMenuOpen ? 'fa-times' : 'fa-bars'}`}></i>
      </button>
    </nav>
  )
}

export default Navigation
