import { useState } from 'react'

const SearchForm = () => {
  const [email, setEmail] = useState('')

  const handleSubmit = (e) => {
    e.preventDefault()
    console.log('Email submitted:', email)
    // Handle form submission here
  }

  return (
    <form 
      className="flex items-center bg-white/5 rounded-xl p-2 max-w-[500px] mb-8 border border-white/10 transition-colors duration-300 focus-within:border-secondary w-full"
      onSubmit={handleSubmit}
    >
      <i className="fa-solid fa-magnifying-glass text-xl text-text-muted mx-4"></i>
      <input
        type="email"
        placeholder="Get Early Access - Enter Your Email"
        required
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        className="flex-grow bg-transparent border-none outline-none text-white text-base font-sans placeholder:text-text-muted"
      />
      <button
        type="submit"
        className="bg-primary text-white border-none rounded-lg py-3 px-7 font-semibold cursor-pointer transition-colors duration-300 hover:bg-secondary"
      >
        START EDITING
      </button>
    </form>
  )
}

export default SearchForm
